<template>
	<view class="page">
		<view class="card xggg">
			<view class="title">
				相关公告
			</view>
			<view class="content">
				<view class="item" v-for="(item, index) in detailData.data?.relevantAnnoList" :key="index"
					@click="jumpDetail(item)">
					<template v-if="item.annoChangeId">
						<view class="type change">
							变更公告
						</view>
					</template>
					<view class="title-text">
						{{ item.proName }}
					</view>
					<image :src="
                    config.imageUrl +
                    '/static/images/changeAnnouncementDetails/arrow.png'
                  " mode="" class="arror"></image>
				</view>
				<view class="item" v-for="(item, index) in detailData.data2" :key="index"
					@click="jumpDetail2(item)">
						<view class="type transaction">
							结果公告
						</view>
					<view class="title-text">
						{{ item.proName }}
					</view>
					<image :src="
				    config.imageUrl +
				    '/static/images/changeAnnouncementDetails/arrow.png'
				  " mode="" class="arror"></image>
				</view>
			</view>
		</view>

		<view class="card ggnr">
			<view class="title">
				公告内容
			</view>

			<view class="content">
				<view class="content-title">
					{{ detailData.data?.title }}
				</view>
				<view class="content-main" v-html="detailData.data?.content">
				</view>
			</view>
		</view>

		<view class="card fjxx" v-if="detailData.fileList&&detailData.fileList.length!=0">
			<view class="title">
				附件信息
			</view>

			<view class="content">
				<view class="file-item" v-for="(item, index) in detailData.fileList" :key="index">
					<view class="title-text">
						{{ item.fileName }}：
					</view>
					<view class="">
						<view class="link" v-for="(ite, ind) in item?.fileList" :key="ind" @click="viewFile(ite)">
							{{ ite.fileName }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import config from "@/common/config";
	import {
		onLoad
	} from "@dcloudio/uni-app";
	import {
		reactive
	} from "vue";

	const {
		http
	} = uni.$u;

	const searchParam = reactive({
		changeAnnoId: "",
		projectId: "",
		tenderId: ""
	})

	const detailData = reactive({
		data: {},
		data2: [],
		fileList: []
	})

	onLoad((option) => {
		searchParam.changeAnnoId = option.changeAnnoId ?? "";
		searchParam.projectId = option.projectId ?? "";
		searchParam.tenderId = option.tenderId ?? "";
		getData();
		getFile();
	})

	/**
	 * 获取数据
	 * 
	 * @returns {void}
	 */
	function getData() {
		const data = {
			changeAnnoId: searchParam.changeAnnoId,
			projectId: searchParam.projectId,
			tenderId: searchParam.tenderId
		}
		const data2 = {
			projectId: searchParam.projectId,
			tenderId: searchParam.tenderId
		}
		http.post(config.baseUrl + "/annoQuery/getResultAnnoChangeDetail", data, {
			method: "POST"
		}).then(r => {
			if (r.data.code !== 200) {
				uni.showToast({
					title: r.data.msg,
					icon: "none",
					duration: 3000,
				});
				return;
			}
			detailData.data = r.data.data;
		})
		
		http.post(config.baseUrl + "/annoQuery/getResultAnno", data2, {
			method: "POST"
		}).then(r => {
			if (r.data.code !== 200) {
				uni.showToast({
					title: r.data.msg,
					icon: "none",
					duration: 3000,
				});
				return;
			}
			detailData.data2.push(r.data.data);
		})
	}

	/**
	 * 获取附件
	 * 
	 * @returns {void}
	 */
	function getFile() {
		// 获取附件
		http.post(config.baseUrl + '/memberApp/file/getTheAttachmentTypeList', {
			busId: searchParam.changeAnnoId,
			projectId: searchParam.projectId,
			flowBasecode: "FLOW_JGBGGG",
			openFlag: 0,
		}, {
			method: 'POST'
		}).then(r => {
			console.log(r);
			if (r.data.code === 200) {
				detailData.fileList = r.data.data || [];
			} else {
				uni.showToast({
					title: r.data.msg,
					icon: "none",
					duration: 3000,
				});
			}
		})
	}

	/**
	 * 跳转详情
	 * 
	 * @param {object} row
	 * @returns {void}
	 */
	function jumpDetail(row) {
		if (row.annoChangeId) {
			// 变更公告
			uni.navigateTo({
				url: "/pages/userInformationView/changeAnnouncementDetails2?changeAnnoId=" + row.annoChangeId +
					"&projectId=" + row.proId + "&tenderId=" + row.tenderId,
			});
		} else if (['15', '12', '13', '14'].includes(row.bidType)) {
			// 招标公告
			uni.navigateTo({
				url: "/pages/userInformationView/zbggDetail?projectId=" +
					row.proId +
					"&tenderId=" +
					row.tenderId +
					"&annoType=2",
			});
		} else {
			// 交易公告
			uni.navigateTo({
				url: "/pages/userInformationView/tradingAnnouncementDetail?projectId=" +
					row.proId +
					"&tenderId=" +
					row.tenderId +
					"&annoType=",
			});
		}
	}
	function jumpDetail2(row) {
		uni.navigateTo({
			url: "/pages/userInformationView/closingAnnouncement?projectId=" +
				row.proId +
				"&tenderId=" +
				row.tenderId +
				"&annoType=",
		});
	}

	/**
	 * 预览图片
	 * @param {Object} url
	 * @returns {void}
	 */
	function viewFile(url) {
		if (url.fileSuffix == "pdf") {
			uni.navigateTo({
				url: '/pages/userInformationView/pdfView?url=' + encodeURIComponent(url.fileUrl),
			});
		} else {
			uni.previewImage({
				current: url, //当前的图片路径
				urls: [ // 所有图片的URL列表，数组格式   预览图片路径
					url.fileUrl
				],
				success: function(res) {
					console.log(res)
				}
			})
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		height: 100%;
		padding: 30rpx 32rpx;

		.card {
			padding: 30rpx 20rpx;
			background: #FFFFFF;
			border-radius: 10px 10px 10px 10px;
			width: 100%;
			overflow: hidden;
			margin-bottom: 30rpx;

			&:nth-last-of-type(1) {
				margin-bottom: 0;
			}

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				line-height: 32rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				margin-bottom: 30rpx;
			}
		}

		.xggg {
			.content {
				.item {
					height: 42rpx;
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					&:nth-last-of-type(1) {
						margin-bottom: 0;
					}

					.type {
						width: 126rpx;
						min-width: 126rpx;
						height: 42rpx;
						border-radius: 10rpx 10rpx 10rpx 10rpx;
						margin-right: 10rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
						line-height: 24rpx;
					}

					.change {
						background: linear-gradient(270deg, #FF8A00 2%, #FF8A00 99%);
					}

					.transaction {
						background: #11B521;
					}

					.title-text {
						flex: 1;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						line-height: 28rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.arror {
						min-width: 32rpx;
						width: 32rpx;
						height: 32rpx;
						margin-left: 7rpx;
					}
				}
			}
		}

		.ggnr {
			.content {
				background: #F7F7F7;
				border-radius: 10rpx 10rpx 10rpx 10rpx;
				padding: 30rpx 20rpx;

				.content-title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 40rpx;
					color: #333333;
					line-height: 55rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
					margin-bottom: 20rpx;
				}

				.content-main {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					line-height: 55rpx;
					text-align: justify;
					font-style: normal;
					text-transform: none;
				}
			}
		}

		.fjxx {
			.content {
				.file-item {
					display: flex;
					align-items: flex-start;
					justify-content: space-between;
					margin-bottom: 10rpx;

					&:nth-last-of-type(1) {
						margin-bottom: 0;
					}

					.title-text {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #666666;
						line-height: 28rpx;
						min-width: 120rpx;
					}

					.link {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 28rpx;
						color: #0098FF;
						line-height: 1.2;
						min-width: 420rpx;
						max-width: 420rpx;
						text-align: right;

						margin-bottom: 10rpx;

						&:nth-last-of-type(1) {
							margin-bottom: 0;
						}
					}
				}
			}
		}


	}
</style>