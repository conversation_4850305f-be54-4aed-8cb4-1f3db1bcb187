<template>
  <view class="detail-box" :class="[5 > 3 ? 'has-btn' : 'no-btn']">
    <!-- 缴款 -->
    <!-- 投标保证金的特殊情况是相对后端来说，对前端无影响 -->
    <!-- 如果存在确认收款流程，则显示缴款确认时间 -->
    <view class="pay-box" v-if="true">
      <!-- 基本信息 -->
      <view class="basic-info">
        <view class="info-text" v-if="detailData.incomeExamineStatus==2 && detailData.payStatus==9 ">请耐心等待交易中心确认收款！</view>
        <view class="title-cont">
          <view class="title">
            基本信息
          </view>
          <view class="card-tag" v-if="detailData.payStatus == 1" style="background: linear-gradient(271deg, #FA4931 0%, #FDA13F 116%);">
            <view class="payment-img">
              <image class="img" :src="config.imageUrl + '/static/images/paymentImgIconLd.png'" alt="" />
            </view>
            <view>{{ detailData.payStatusName }}</view>
          </view>
          <view class="card-tag" v-else>
            <view class="payment-img">
              <image class="img" v-if="detailData.payStatus == 10 || detailData.operateType == 11 || detailData.operateType == 4" :src="config.imageUrl + '/static/images/paymentImgIconClose.png'" alt="" />
              <image class="img" v-else-if="detailData.payStatus == 2 || detailData.incomeExamineStatus == 2" :src="config.imageUrl + '/static/images/paymentImgIconLd.png'" alt="" />
              <image class="img" v-else-if="detailData.payStatus == 9" :src="config.imageUrl + '/static/images/paymentImgIconOk.png'" alt="" />
            </view>
            <view v-if="detailData.operateType == 4">退款</view>
            <view v-else-if="detailData.operateType == 11">异常退款</view>
            <view v-else-if="detailData.incomeExamineStatus == 2">缴款待确认</view>
            <view v-else>{{ detailData.payStatusName }}</view>
          </view>
        </view>
        <view class="info-cont">
          <view class="info-item">
            <view class="item-name">
              项目名称
            </view>
            <view class="item-value">
              {{ detailData.projectName }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              项目编号
            </view>
            <view class="item-value">
              {{ detailData.projectCode }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              费用类型
            </view>
            <view class="item-value">
              <view v-for="item in detailData.costInFeeDetailVoList" :key="item.feeTypeName">
                <view v-if="detailData.busType == 3 && item.feeType==3">
                  {{ item.feeTypeName }} {{item.needPayAmount ? '(' + item.marginNum + '份，'  + '共' + item.needPayAmount + '元)' : ''}}
                </view>
                <view v-else>
                  {{ item.feeTypeName }} {{item.needPayAmount ? '(' + item.needPayAmount + '元)' : ''}}
                </view>
              </view>
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              应缴金额合计
            </view>
            <view class="item-value">
              {{ detailData.needTotalAmount
                                ? detailData.needTotalAmount + "元" +
                                  "  " +
                                  "（" +
                                  toChies(
                                      detailData.needTotalAmount.toString()
                                  ) +
                                  "）"
                                : "" }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              截止缴款时间
            </view>
            <view class="item-value">
              {{ detailData.latePayTime }}
            </view>
          </view>
          <view class="line-dot"></view>
        </view>

      </view>
      <!-- 缴款信息 -->
      <view class="pay-info" v-if="detailData.operateType == 1">
        <view class="title-cont">
          <view class="title">
            缴款信息
          </view>
        </view>
        <view class="info-cont">
          <view class="info-item">
            <view class="item-name">
              业务流水号
            </view>
            <view class="item-value">
              {{ detailData.bankOrderNumber }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              支付方式
            </view>
            <view class="item-value">
              {{ detailData.payTypeName }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              缴款备注
            </view>
            <view class="item-value">
              {{ detailData.remark }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              下单时间
            </view>
            <view class="item-value">
              {{ detailData.opreateTime }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              缴款到账时间
            </view>
            <view class="item-value">
              {{ detailData.secondTime }}
            </view>
          </view>
          <!--有缴款确认流程才有缴款确认时间  -->
          <view class="line" v-if="flowData&&flowData.length>0"></view>
          <view class="info-item" v-if="flowData&&flowData.length>0">
            <view class="item-name">
              缴款确认时间
            </view>
            <view class="item-value">
              {{ detailData.surePayTime }}
            </view>
          </view>
        </view>
      </view>
      <!-- 出金信息 -->
      <view class="pay-info" v-if="(detailData.operateType == 4 || detailData.operateType == 5)">
        <view class="title-cont">
          <view class="title">
            出金信息
          </view>
        </view>
        <view class="info-cont">
          <view class="info-item">
            <view class="item-name">
              业务流水号
            </view>
            <view class="item-value">
              {{ detailData.bankOrderNumber }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              出金金额
            </view>
            <view class="item-value">
             {{ detailData.amount }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              出金去向
            </view>
            <view class="item-value">
              {{ detailData.operateTypeName }}
            </view>
          </view>
          <view class="line"></view>
          <view class="info-item">
            <view class="item-name">
              流水发生时间
            </view>
            <view class="item-value">
              {{ detailData.recordTime }}
            </view>
          </view>
        </view>
      </view>
      <!-- 异常退款原因 -->
      <view class="sure-flow" v-if="detailData.operateType == 11">
        <view class="title-cont">
          <view class="title">
            异常退款原因
          </view>
        </view>
        <view class="flow-cont">
          {{ detailData.remark }}
        </view>

      </view>
      <!-- 确认流程 -->
      <view class="sure-flow" v-if="!(detailData.operateType == 4 || detailData.operateType == 5) && flowData?.length>0">
        <view class="title-cont">
          <view class="title">
            缴款确认流程
          </view>
        </view>
        <view class="flow-cont time-line">
          <up-steps current="2" direction="column" activeColor="#1bbf9a" inactiveColor="#1bbf9a">
            <template v-for="(item,index) in flowData" :key="index" >
            <view class="time-line-title">
              <template v-if="item?.opreateType === 1">提交人：{{item?.opreateName}}</template>
              <template v-else-if="item?.opreateType === 2">确认人：{{item?.opreateName}}</template>
            </view>
            <up-steps-item :error="checkResult(item)">
              <template #icon>
                <view v-if="checkResult(item)" class="slot-icon error-step">
                  <up-icon name="close" color="#fff" size="14"></up-icon>
                </view>
                <view v-else class="slot-icon success-step">
                  <up-icon name="checkmark" color="#fff" size="14"></up-icon>
                </view>
              </template>
              <template #desc>
                <view v-if="item?.opreateType === 1" class="slot-desc">{{item?.opreateTime}}</view>
                <view v-else-if="item?.opreateType === 2" class="slot-desc">
                  <view class="desc-item">
                    确认时间：{{item?.opreateTime}}
                  </view>
                  <view class="desc-item">
                    确认结果：{{item?.opreateResult === 1 ? '通过' : '不通过'}}
                  </view>
                  <view class="desc-item">
                    收款意见：{{item?.suggest}}
                  </view>
                </view>
              </template>
            </up-steps-item>
            </template>
            <!-- 	<up-steps-item>
							<template #icon>
								<view class="slot-icon success-step">
									<up-icon name="checkmark" color="#fff" size="14"></up-icon>
								</view>
							</template>
							<template #title>
								<view class="slot-title">提交人：张三</view>
							</template>
							<template #desc>
								<view class="slot-desc">2023-12-12 10:23</view>
							</template>
						</up-steps-item> -->
            <!-- :error="false"控制线的颜色 -->
            <!-- 	<up-steps-item :error="true">
							<template #icon>
								<view class="slot-icon error-step">
									<up-icon name="close" color="#fff" size="14"></up-icon>
								</view>
							</template>
							<template #title>
								<view class="slot-title">确认人：李四</view>
							</template>
							<template #desc>
								<view class="slot-desc">
									<view class="desc-item">
										确认时间：2023-12-12 10:23
									</view>
									<view class="desc-item">
										确认结果：通过
									</view>
									<view class="desc-item">
										收款意见：同意
									</view>
								</view>
							</template>
						</up-steps-item> -->
          </up-steps>
        </view>

      </view>
    </view>
  </view>
  <!-- 查看缴款通知单 -->
  <!-- 缴款且是转账汇款方式才显示查看缴款通知单按钮，如果是现金支付或者交易方自行结算，则显示缴款凭证查看按钮 -->
  <view class="view-notice" v-if="detailData.operateType != 11 && detailData.operateType != 4">
    <!-- 转账汇款 -->
    <view class="btn" v-if="PAY_TYPE.TRANSFER_REMITTANCE.includes(detailData.payType)" @click="toPayNotice(detailData.orderId)">
      查看缴款通知单
    </view>
    <!-- 现金支付后者交易方自行结算 -->
    <view class="btn" v-if="PAY_TYPE.CASH_OR_SELF_CLEARING.includes(detailData.payType)" @click="viewFile">
      缴款凭证查看
    </view>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { reactive, ref, onMounted, onUnmounted } from "vue";

const { http } = uni.$u;

import config from "@/common/config";
import { toChies } from "./wordFigure";
// onMounted(() => {
// 	// 当前组件挂载时修改 error 颜色
// 	uni.$u.color.error = 'yellow';
// });
// onUnmounted(() => {
// 	// 组件销毁时恢复默认颜色
// 	uni.$u.color.error = '#f56c6c';
// });

/** 缴款方式 */
const PAY_TYPE = {
  /** 转账汇款 @type {number[]} */
  TRANSFER_REMITTANCE: [2],
  /** 现金支付&交易方自行结算 @type {number[]} */
  CASH_OR_SELF_CLEARING: [1, 7, 0],
  /** 聚合支付、扫码支付、数字人民币支付、POS机刷卡、投标保证金、履约保证金转入 @type {number[]} */
  OTHER: [3, 4, 6, 5, 8, 10],
};

const detailParams = ref({});

onLoad((options) => {
  console.log(options, "接收的参数");
  detailParams.value = options;
  getDetailData();
  // 有缴款流程才调用
  getFlowData();
});

let detailData = ref({});

// 获取数据
function getDetailData() {
  let params = {
    bankOrderNumber: detailParams.value.bankOrderNumber,
    feeId: detailParams.value.feeId,
    proId: detailParams.value.projectId,
    costBusinessStatementId: detailParams.value.costBusinessStatementId,
  };
  http
    .post(config.baseUrl + "/billRecord/costInOutPayDetail", params, {
      method: "get",
    })
    .then((r) => {
      if (r.data.code === 200) {
        detailData.value = r.data.data;
      } else {
        uni.showToast({
          title: r.data.msg,
          icon: "none",
          duration: 2000,
        });
      }
    });
}

const flowData = ref([]);

// 获取缴款流程
const getFlowData = () => {
  let params = {
    feeId: detailParams.value.feeId,
  };
  http
    .post(config.baseUrl + "//billRecord/getInComeFlow", params, {
      method: "get",
    })
    .then((r) => {
      console.log(r, "缴款流程");
      if (r.data.code === 200) {
        flowData.value = r.data.data;
      } else {
        uni.showToast({
          title: r.data.msg,
          icon: "none",
          duration: 2000,
        });
      }
    });
};

const submit = () => {
  http
    .post(
      config.baseUrl + "/appSupply/security/talk",
      {},
      {
        method: "post",
      }
    )
    .then((r) => {
      if (r.data.code === 200) {
      } else {
        uni.showToast({
          title: r.data.msg,
          icon: "none",
          duration: 2000,
        });
      }
    });
};

// 判断是true还是return false
const checkResult = (item) => {
  return item?.opreateType === 2 && item?.opreateResult === 0;
};

//查看缴款通知单
function toPayNotice(id) {
  uni.navigateTo({
    url:
      "/pages/myView/myview/compontents/myProject/components/payNotices?orderId=" +
      id,
  });
}

// 缴款凭证查看
function viewFile() {
  let id = ''
  if(detailData.value.batchFlag == 1){
    id = detailData.value.feeIds;
  }else{
    id = detailData.value.feeId;
  }
  uni.navigateTo({
    url:
      "/pages/myView/myview/compontents/billRecord/compontents/paymentVoucher?orderId=" +
      id,
  });
}
</script>

<style lang="scss" scoped>
@import "../css/payment.scss";
@import "../css/withdraw.scss";
@import "../css/refund.scss";

.detail-box {
  width: 100vw;
  padding: 30rpx 32rpx 0;
  min-height: 100vh;
  box-sizing: border-box;
}

.has-btn {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 212rpx) !important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 212rpx) !important;
}

.no-btn {
  padding-bottom: 30rpx;
}

.view-notice {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 172rpx;

  /* 自动布局 */
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;

  .btn {
    width: 686rpx;
    height: 86rpx;
    border-radius: 10rpx;

    /* 自动布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    background: #0098ff;
    font-family: PingFang SC;
    font-size: 36rpx;
    font-weight: normal;
    line-height: 36rpx;
    text-align: center;
    letter-spacing: 0em;
    color: #ffffff;
  }
}
.title-cont {
  align-items: center;
  .title {
    font-weight: 550;
  }
}
.card-tag {
  height: 42rpx;
  background: linear-gradient(90deg, #38c1ee 0%, #0098ff 100%);
  display: flex;
  align-items: center;
  padding-right: 20rpx;
  font-family: PingFang SC;
  font-size: 24rpx;
  font-weight: normal;
  letter-spacing: 0em;
  border-radius: 10rpx;
  color: #ffffff;
  .payment-img {
    width: 42rpx;
    height: 42rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    background: rgba(255, 255, 255, 0.25);
    .img {
      width: 36rpx !important;
      height: 36rpx !important;
    }
  }
}
.time-line{
  :deep(.u-text){
    height: 50rpx;
    flex: none;
  }
  .time-line-title{
    position: relative;
    height: 0;
    top:0;
    left:58rpx;
    color:#333;
    font-family: PingFang SC;
    font-size: 28rpx;
  }
}
</style>