<template>
	<view class="wrap-box" :class="theme">
		<view class="records">
			<!-- 单位会员实名才展示 -->
			<view class="" v-if="resData.userType === '2' && resData.registerIdentity == 2">
				<view class="title">
					<img style="width: 30rpx;height: 30rpx;margin-right: 10rpx;"
						:src="`${config.imageUrl}/static/images/user/edit-icon.png`" />
					委托人信息
				</view>
				<view class="records-list">
					<view>
						<view>证件类型</view>
						<view style="text-align: right;width: 75%;">
							{{ temp.legalType }}
						</view>
					</view>
					<view>
						<view>真实姓名</view>
						<view>{{ resData.registerName }}</view>
					</view>
					<view>
						<view>证件号</view>
						<view>{{ resData.registerNo }}</view>
					</view>
				</view>
				<view style="height: 32rpx; width: 100%;"></view>
			</view>

			<view class="title" v-if="resData.userType === '2' && resData.registerIdentity == 2">
				<img style="width: 30rpx;height: 30rpx;margin-right: 10rpx;"
					:src="`${config.imageUrl}/static/images/user/edit-icon.png`" />
				法人信息
			</view>
			<view class="records-list">
				<view>
					<view>备案方式</view>
					<view style="text-align: right;width: 75%;">
						{{ resData.realNameType == 1 ? '线上备案' : resData.realNameType == 2 ? '线下备案' : '--' }}
					</view>
				</view>
				<view>
					<view>证件类型</view>
					<view style="text-align: right;width: 75%;">
						{{ temp.registerType }}
					</view>
				</view>
				<view>
					<view>真实姓名</view>
					<view>{{ resData.legalPersonName }}</view>
				</view>
				<view>
					<view>证件号</view>
					<view>{{ resData.legalPersonNo }}</view>
				</view>
				<view v-if="resData.realNameType == 1">
					<view>开户银行</view>
					<view>{{ resData.openBankName }}</view>
				</view>
				<view v-if="resData.realNameType == 1">
					<view>开户银行行号</view>
					<view>{{ resData.openBankNo }}</view>
				</view>
				<view v-if="resData.realNameType == 1">
					<view>银行卡号</view>
					<view>{{ resData.bankAccount }}</view>
				</view>
				<view v-if="resData.realNameType == 1">
					<view>银行预留手机号</view>
					<view><u--input class="bank-input" placeholder="请填写银行预留手机号" maxlength="11" type="number"
							border="none" v-model="form.legalPersonPhone"></u--input></view>
				</view>
				<view v-if="resData.realNameType == 1">
					<view>短信验证码</view>
					<view class="authentication">
						<u--input placeholder="请填写短信验证码" type="number" border="none" maxlength="6"
							v-model="form.code"></u--input>
						<view class="button">
							<!-- <phone-code :telephone="temp.legalPersonPhone" /> -->
							<phone-code :telephone="form.legalPersonPhone" />
						</view>
					</view>
				</view>
				<view v-if="resData.realNameType != 1 && false">
					<view>备案地区</view>
					<view
						style="width: 350rpx;display: inline-block;white-space: wrap;word-break: break-all;text-align: right;">
						{{ areaName }}</view>
				</view>
			</view>


			<view class="button-finash">
				<view @click="getDetail">查看脱敏信息</view>
			</view>
		</view>
		<!-- isAudit 是false 置灰不可点击 -->
		<view class="button-finash">
			<view @click="$u.throttle(getRecords, 2000)" :class="{ 'ondis': !isAudit }">{{ isAudit ? '修改备案信息' : '修改审核中'
			}}</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import config from '@/common/config.js';
import phoneCode from '@/pages/myView/myview/compontents/phoneCode.vue';
import { encryptByAESarr } from '@/util/crypto-js/index.js';
const { http } = uni.$u;
const theme = config.theme;

const prePageData=ref({})

onLoad((option) => {
	prePageData.value=option
	getRes()
})
//form(表单数据) ========================================================
const form = reactive({
	userId: uni.getStorageSync('user').userId,
	type: 1, // 1为脱敏数据，2为真实数据
	legalPersonPhone: "", //预留手机号 legalPersonPhone
	code: "", //短信验证 code
});
const resData = ref("")

const temp = reactive({
	registerType: "",
	legalType: "222",
	legalPersonPhone: ""
})
// 表单校验提示
const formVerifyAlert = (type, skip) => {
	uni.showToast({
		title: type,
		icon: 'none',
		duration: 2500
	});
};
const areaName = ref('')
const getRes = () => {
	http.post(config.baseUrl + `/memberApp/memberName/nameMessage`, {
		type: 1,
		userId: form.userId
	}).then(async res => {
		if (res.data.code !== 200) return
		resData.value = res.data.data

		// console.log(getaeraName(res.data.data.realNameAreaAllPath),'getaeraName(res.data.data.realNameAreaAllPath)')
		// areaName.value= await getaeraName(res.data.data.realNameAreaAllPath);
		areaName.value = res.data.data.realNameAreaName;
		// form.legalPersonPhone = resData.value.legalPersonPhone
		// 个人或法人证件类型
		if (resData.value.idType) {
			switch (resData.value.idType) {
				case "1":
					temp.registerType = "身份证"
					break;
				case "2":
					temp.registerType = "护照"
					break;
				case "3":
					temp.registerType = "港澳居民来往内地通行证"
					break;
				case "4":
					temp.registerType = "台湾居民来往大陆通行证"
					break;
			}
		}
		// 委托人证件类型
		if (resData.value.registerType) {
			switch (resData.value.registerType) {
				case "1":
					temp.legalType = "身份证"
					break;
				case "2":
					temp.legalType = "护照"
					break;
				case "3":
					temp.legalType = "港澳居民来往内地通行证"
					break;
				case "4":
					temp.legalType = "台湾居民来往大陆通行证"
					break;
			}
		}
		// 获取不脱敏的手机号
		// http.post(config.baseUrl + `/memberApp/memberName/decrypt`,{encryptMessage: resData.value.legalPersonPhoneSecret},{method:"GET"}).then(res=>{
		// 	if(res.data.code!==200) return
		// 	temp.legalPersonPhone = res.data.data
		// })
	})
}

// 完成 
const getDetail = async () => {
	// return console.log(temp.legalPersonPhone,"temp.legalPersonPhone")
	if (resData.value.realNameType == 1 && !form.legalPersonPhone) return formVerifyAlert("手机号不能为空")
	else if (resData.value.realNameType == 1 && !form.code) return formVerifyAlert("验证码不能为空")
	// let encvryptParams = await encryptByAESarr([
	// 	{ name: "legalPersonPhone", value: resData.value.legalPersonPhoneSecret },
	// ])
	http.post(config.baseUrl + '/memberApp/memberName/nameMessage	', {
		...form,
		type: 2,
		// legalPersonPhone: temp.legalPersonPhone,
		legalPersonPhone: form.legalPersonPhone,
	}).then(res => {
		if (res.data.code == 200) {
			formVerifyAlert("查看成功")
			resData.value = res.data.data

			// form.legalPersonPhone = temp.legalPersonPhone
		} else {
			formVerifyAlert(res.data.msg);
		}
	})
}

async function getaeraName(aeraIds) {
	let areaIdList = aeraIds.split('|'); // 将地区id字符串按'|'分割成数组

	let areaNames = []; // 存储地区名称的数组

	for (let areaId of areaIdList) {
		let areaName = await getArea(areaId); // 使用await等待异步操作完成并获取地区名称
		areaNames.push(areaName); // 将地区名称存入areaNames数组
	}

	return areaNames.join('/'); // 将地区名称数组拼接成一个字符串并返回
}

async function getArea(areaId) {
	let params = {
		areaId: areaId,
		userToken: 0,
		businessOutsideFleg: 0
	};

	try {
		let response = await http.post(config.baseUrl + '/memberApp/memberName/getRealNameArea?areaId=' + areaId, params, { method: 'get' });
		if (response.data.code === 200) {
			return response.data.data[0].name; // 假设地区名称在返回数据的第一个对象的'name'字段中
		} else {
			uni.showToast({
				title: response.data.msg,
				icon: 'none',
				duration: 3000
			});
			return ''; // 返回空字符串表示获取地区名称失败
		}
	} catch (error) {
		console.error(error);
		return ''; // 返回空字符串表示获取地区名称失败
	}
}

// 判断是否正在审核的备案
const isAuditfun = () => {
	return new Promise((resolve, reject) => {
		http.post(config.baseUrl + `/memberApp/memberName/checkRealNameUpdatingUsers`, {}, {
			method: 'GET'
		}).then(res => {
			if (res.data.code === 200) {
				isAudit.value = res.data.data
				resolve(res.data.data)
			} else {
				reject(res.data.msg)
			}
		}).catch(err => {
			reject(err)
		})
	})
}

// 修改实名认证
const getRecords = async type => {
	try {
		// 检查是否有审核中的备案
		const auditStatus = await isAuditfun()

		if (!auditStatus) {
			uni.showToast({
				title: '修改审核中',
				icon: 'none',
				duration: 2000
			});
			return
		}

		http.post(config.baseUrl + `/memberApp/memberName/getRealameStatus`, {}, {
			// http.post(config.baseUrl + `/memberApp/memberName/getNameStatus`, {}, {
			method: 'GET'
		}).then(res => {
			if (res.data.code === 200) {
				// 修改实名认证走的是新接口，status为空说明没有备案修改记录，所以初始值=2是备案审核成功的状态
				if (!res.data.data.status) res.data.data.status = 2
				// 添加标志位，表明是实名修改
				res.data.data.isRealName = true
				res.data.data.userType = prePageData.value.userType || ''
				res.data.data.registerIdentity = prePageData.value.registerIdentity || ''
				res.data.data.telePhoneSecret = prePageData.value.telePhoneSecret || ''
				// return
				uni.$u.route('/pages/myView/myview/compontents/recordsView', res.data.data);
			} else {
				uni.showToast({
					title: res.data.msg,
					icon: 'none',
					duration: 2000
				});
			}
		});
	} catch (err) {
		uni.showToast({
			title: err || '检查审核状态失败',
			icon: 'none',
			duration: 2000
		});
	}
};

const isAudit = ref(false)



onShow(() => {
	// 初始化时调用
	isAuditfun().catch(err => {
		console.error('初始化检查审核状态失败:', err)
	})
})



</script>

<style lang="scss">
.ondis {
	opacity: 0.5;
	pointer-events: none;
	cursor: not-allowed;
	background-color: #cccccc;
	color: #666666;
}

.bot-btn {
	margin: 30rpx auto;

	button {
		height: 86rpx;
		background: #0098ff;
		border-radius: 10rpx;
		font-size: 36rpx;
		font-weight: 400;
		color: #ffffff;
		line-height: 86rpx;
	}
}

.card-title {
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: #000;
	line-height: 28rpx;
	margin-bottom: 24rpx;
	display: flex;
	align-items: center;
}

.title {
	background: #fff;
	padding-left: 32rpx;
	color: #000;
	font-size: 30rpx;
	height: 80rpx;
	line-height: 80rpx;
	display: flex;
	align-items: center;
	border-radius: 16rpx 16rpx 0 0;
}

.wrap-box {
	width: calc(100% - 32rpx);
	margin: auto;
	padding-bottom: 20rpx;
	margin-top: 20rpx;
}

.records {
	.card {
		padding: 30rpx;
		background-color: #fff;
		margin-bottom: 30rpx;
		border-radius: 16rpx;

		.card-biall {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;

			view {
				position: relative;
				width: 100%;
				height: 307rpx;
				background: #f2f8ff;
				border-radius: 10rpx;

				image {
					display: block;
					margin: 0 auto;
					width: 100%;
					height: 100%;
				}

				text {
					position: absolute;
					top: 219rpx;
					width: 100%;
					text-align: center;
					font-size: 30rpx;
					color: #0098FF;
				}
			}

			.card-whole {
				margin-bottom: 28rpx;
			}
		}
	}

	.records-explain {
		// background: rgba(26, 123, 234, 0.1);
		padding: 16rpx 40rpx 16rpx 30rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #666666;
		opacity: 0.82;
		line-height: 30rpx;
	}

	.records-list {
		background: #fff;
		border-radius: 0 0 16rpx 16rpx;

		&>view {
			color: #333;
			// height: 80rpx;
			min-height: 88rpx;
			margin: 0 32rpx;
			display: flex;
			justify-content: space-between;
			border-bottom: 1rpx solid #ebebeb;
			align-items: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 400;
		}

		&>view:last-child {
			border-bottom: 0;
		}

		view:nth-child(1) {
			// color: #666666;
			white-space: nowrap;
		}

		view:nth-child(2) {
			color: #333333;
		}

		.u-input__content__field-wrapper__field {
			text-align: right !important;
			font-size: 28rpx !important;
		}

		.bank {
			.u-input__content__field-wrapper__field {
				width: 540rpx !important;
				// background-color: #007AF5;
			}

			.bank-conduction {
				color: #c0c4cc;
				font-size: 28rpx;
			}

			.conduction-one {
				color: #303133;
			}
		}

		.authentication {
			display: flex;
			justify-content: flex-end;
			align-items: center;

			.button {
				width: 184rpx;
				height: 52rpx;
				// margin-left: 20rpx;
				// background: #f4f4f4;
				border-radius: 26rpx;
				text-align: right;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				// color: #1a7bea;
				line-height: 52rpx;

				.button-text {
					color: #666666;
				}
			}
		}
	}

	.records-upload {
		padding: 7rpx 29rpx 0 29rpx;
		background: #fff;
		border-radius: 0 0 16rpx 16rpx;

		.upload-whole {
			display: flex;
			margin-bottom: 32rpx;

			:deep(.u-upload) {
				flex: none;
			}
		}

		.upload-moudle {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-right: 43rpx;

			.upload-img {
				margin-bottom: 20rpx;
				width: 148rpx;
				height: 148rpx;
			}

			text {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 1;
			}

			.margin-class {
				padding-top: 10rpx;
			}
		}

		.upload-book {
			display: flex;
			align-items: center;
			justify-content: space-between;
			// margin-bottom: 16rpx;
			padding: 16rpx 20rpx;
			height: 60rpx;
			background: #f7f7f7;
			border-radius: 8rpx;

			&>view {
				display: flex;
				align-items: center;

				image {
					margin-right: 13rpx;
					width: 24rpx;
					height: 27rpx;
				}

				text {
					color: #0098FF;
					font-size: 28rpx;
				}
			}

			&>image {
				width: 18rpx;
				height: 18rpx;
			}
		}

		.upload-book:last-child {
			margin-bottom: 16rpx;
		}

		// .upload-book.book-bottom{
		// 	margin-bottom: 0;
		// }
		.bottom {
			height: 32rpx;
			width: 100%;
		}
	}


}

.button-finash {
	width: 100%;
	// position: fixed;
	// bottom: 50rpx;
	// left: 0;
	padding: 0 32rpx;
	margin-top: 40rpx;

	view {
		width: 100%;
		height: 88rpx;
		background: var(--light-left);
		border-radius: 10rpx;
		font-size: 36rpx;
		text-align: center;
		font-family: PingFang SC;
		font-weight: 400;
		color: #f6f8fa;
		line-height: 88rpx;
	}

	.ondis {
		opacity: 0.8;
	}
}
</style>