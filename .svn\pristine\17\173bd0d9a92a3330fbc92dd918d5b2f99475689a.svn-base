<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="产权产品mini" src="/leadingEnd/cq-mini" version="2025-08-01 20:00">
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<script>
			document.write('<script src="./static/config2.js?t=' + new Date().getTime() + '"><\/script>')
		</script>
		<script type="text/javascript" src="https://kfpt.ihuanggang.cn/jssdk/js/jssdk.js"></script>
		<title></title>
		<!--preload-links-->
		<!--app-context-->
	</head>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/src/main.js"></script>
	</body>
</html>