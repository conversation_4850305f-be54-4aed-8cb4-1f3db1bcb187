<template>
	<view :style="`width:100vw; height: calc(100vh - 200rpx); overflow-y: auto; margin: auto; padding: 70rpx 60rpx; background:url(${config.imageUrl}/static/images/paymentNotice.png); background-repeat: no-repeat; background-size: cover;`">
		<view style=" text-align: center;
	      font-size: 36rpx;
	      font-weight: bold;
	      color: #3d3d3d;
	      margin-bottom: 80rpx;font-family: 'SimSun'">
			农村产权交易服务平台缴款通知单
		</view>
		<view>
			<view style=" font-size: 28rpx;
	        font-weight: 700;
	        color: #3d3d3d;">
				尊敬的{{pageData.dealName}}：
			</view>
			<view style="  
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 1.5;text-indent: 2em;">
				您好！为避免影响您参与交易，请您按以下要求及时缴纳款项。
			</view>
		</view>
		<view>
			<view style="margin: 20rpx 0;  font-size: 28rpx;
	        font-weight: 700;
	        color: #3d3d3d;">
				缴款信息
			</view>
			<view style="
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				项目名称：{{ pageData.proName }}—标段{{pageData.tendersCode}}({{pageData.projectCode}})
			</view>
			<view style="  
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				缴纳费种：{{ pageData?.payFeeNameList?.join('、') }}
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				缴纳金额：{{pageData.dealMoney}}元 &nbsp;&nbsp;{{convertCurrency(pageData.dealMoney)}}
			</view>
			<view style="  
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				缴费截止时间：{{pageData.payEndTime || ''}}
			</view>
		</view>
		<view>
			<view style=" margin: 20rpx 0; font-size: 28rpx;
	        font-weight: 700;
	        color: #3d3d3d;">
				收款人信息
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				收款户名：{{pageData.virtualAccountName}}
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 28rpx;
	        color: #444444;
	        line-height: 2;">
				收款账号：{{pageData.virtualAccount}}
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				开户银行：{{pageData.bankName}}
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				开户网点：{{pageData.openingBank||'--'}}
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				开户行号：{{pageData.bankNo||'--'}}
			</view>
		</view>
		<view>
			<view style=" margin: 20rpx 0;      font-size: 14px;
	        font-weight: 700;
	        color: #3d3d3d;">
				特别提示：
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				1、请按本通知单要求，一次性足额缴费。
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				2、为避免影响参与交易，请您至少在缴款截止时间前15分钟完成缴款。
			</view>
			<view style="  
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				3、请您使用通过系统实名认证的银行卡进行缴款，否则无法缴款。
			</view>
			<view style="   
	        font-weight: 400;
	        font-size: 14px;
	        color: #444444;
	        line-height: 2;">
				4、如有疑问，可与交易中心进行联系。
			</view>
		</view>
	</view>
	<view class="submit-box">
		<button class="submit" @click="downloadImage">下载</button>
		<button class="submit plain" @click="submit">关闭</button>
		<button class="submit plain" @click="copyHandle">复制收款账号</button>
	</view>
</template>
<script>
	const { http } = uni.$u;
	import config from '@/common/config';
	import { encode, decode } from 'js-base64';

	export default {
		data() {
			return {
				orderId: '',
				pageData: {},
				filePath: '',
				imageUrl: ''
			};
		},
		onLoad(option) {

			this.orderId = option.orderId;
			this.getPaymentPageData();
		},
		methods: {
			getPaymentPageData() {
				http.post(config.baseUrl + `/appPay/getOrderInfo/${this.orderId}`, {}, {
					method: 'get'
				}).then(res => {
					if (res.data.code != 200) {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 3000
						});
					} else {
						this.pageData = res.data.data || {};
					}
				});
			},
			copyAccount(val) {
				uni.setClipboardData({
					data: val,
					success: function() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none',
							duration: 2000
						});
					},
					fail(err) {
						uni.showToast({
							title: err,
							duration: 2000
						});
					}
				});
			},
			// 复制收款账号（兼容H5和小程序）
			copyHandle() {
				const account = this.pageData.virtualAccount || '';

				// 微信小程序环境
				// #ifdef MP-WEIXIN
				if (account) {
					uni.setClipboardData({
						data: account,
						success: () => {
							uni.showToast({
								title: '复制成功',
								icon: 'none'
							});
						},
						fail: (err) => {
							uni.showToast({
								title: '复制失败',
								icon: 'none'
							});
							console.error('复制失败:', err);
						}
					});
				} else {
					uni.showToast({
						title: '无收款账号可复制',
						icon: 'none'
					});
				}
				// #endif

				// H5 环境
				// #ifdef H5
				if (!account) {
					uni.showToast({
						title: '无收款账号可复制',
						icon: 'none'
					});
					return;
				}

				// 现代浏览器（推荐）
				if (navigator.clipboard && navigator.clipboard.writeText) {
					navigator.clipboard.writeText(account)
						.then(() => {
							uni.showToast({
								title: '复制成功',
								icon: 'none'
							});
						})
						.catch(err => {
							console.error('复制失败:', err);
							this.fallbackCopy(account); // 降级方案
						});
				} else {
					// 旧浏览器降级方案
					this.fallbackCopy(account);
				}
				// #endif
			},

			// H5旧浏览器的复制降级方案
			fallbackCopy(text) {
				const textarea = document.createElement('textarea');
				textarea.value = text;
				textarea.style.position = 'fixed';
				document.body.appendChild(textarea);
				textarea.select();

				try {
					const success = document.execCommand('copy');
					document.body.removeChild(textarea);
					if (success) {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				} catch (err) {
					document.body.removeChild(textarea);
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
					console.error('复制失败:', err);
				}
			},
			submit() {
				console.log('跳转列表页');
				// uni.redirectTo({
				// 	url: '/pages/myView/myview/compontents/myProject/myProject',
				// });
				uni.navigateBack()
			},
			async downloadImage() {

				let htmlString = encode(this.generatePaymentNoticeHtml(this.pageData));
				// 调用后端接口获取图片
				http.post(config.baseUrl + '/appPay/getOrderInfoPic', {
						// http.post('https://vx.jsnc.gov.cn/cq-app/appPay/getOrderInfoPic', {
						orderId: this.orderId,
						// orderId: new Date().getTime(),
						htmlStr: htmlString
					}, { method: 'post' })
					.then(res => {
						console.log(res, 'res')
						if (res.data.code === 200) {
							console.log('请求成功')
							this.imageUrl = res.data.data.fileUrl; // imageUrl
							this.saveImage()
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
					.catch(err => {
						console.error(err);
						uni.showToast({
							title: '获取图片失败',
							icon: 'none'
						});
					});
			},
			saveImage() {
				// #ifdef MP-WEIXIN
				// 检查用户是否授权
				wx.getSetting({
					success: (res) => {
						if (res.authSetting['scope.writePhotosAlbum']) {
							this.saveToAlbum(this.imageUrl);
						} else {
							wx.authorize({
								scope: 'scope.writePhotosAlbum',
								success: () => {
									this.saveToAlbum(this.imageUrl);
								},
								fail: () => {
									wx.showModal({
										title: '授权提示',
										content: '需要您授权保存到相册，请在设置中打开权限。',
										showCancel: false
									});
								}
							});
						}
					}
				});
				// #endif
				// #ifdef H5
				if (!this.imageUrl) {
					alert('没有可保存的图片');
					return;
				}
				uni.navigateTo({
					url: '/pages/myView/myview/compontents/myProject/components/payNoticesImg?img=' + this.imageUrl
				})
				// #endif
			},
			saveToAlbum(imageUrl) {
				// imageUrl = "http://172.168.23.127:8080/manage/file/getFileImage?fileUrl=/2025/01/21/20250121182629A001.png"
				// console.log(imageUrl, 'imageUrl')
				wx.downloadFile({
					url: imageUrl,
					success: (res) => {
						console.log(res, 'res')
						// let path = res.tempFilePath+'.png'
						let path = res.tempFilePath
						console.log(path, 'path')
						if (res.statusCode === 200) {
							wx.saveImageToPhotosAlbum({
								filePath: path,
								success: () => {
									wx.showToast({
										title: '已下载图片保存至相册',
										icon: 'none'
									});
								},
								fail: (err) => {
									console.error('保存失败', err);
								}
							});
						}
					},
					fail: (err) => {
						console.error('下载失败', err);
						uni.showToast({
							title: '下载图片失败',
							icon: 'none'
						});
					}
				});
			},
			//数字转大写
			convertCurrency(money) {
				// 汉字的数字
				let cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
				// 基本单位
				let cnIntRadice = ['', '拾', '佰', '仟']
				// 对应整数部分扩展单位
				let cnIntUnits = ['', '万', '亿', '兆']
				// 对应小数部分单位
				let cnDecUnits = ['角', '分', '毫', '厘']
				// 整数金额时后面跟的字符
				let cnInteger = '整'
				// 整型完以后的单位
				let cnIntLast = '元'
				// 最大处理的数字
				let maxNum = 999999999999.9999

				// 输出的中文金额字符串
				let chineseStr = ''

				if (money === '') {
					return ''
				}

				money = parseFloat(money)

				if (money >= maxNum) {
					// 超出最大处理数字
					return ''
				}

				if (money === 0) {
					return cnNums[0] + cnIntLast + cnInteger
				}

				// 转换为字符串
				money = money.toString()
				let [integerNum, decimalNum] = money.split('.')

				// 处理整数部分
				if (parseInt(integerNum, 10) > 0) {
					let zeroCount = 0
					const IntLen = integerNum.length

					for (let i = 0; i < IntLen; i++) {
						let n = integerNum.charAt(i)
						let p = IntLen - i - 1
						let q = Math.floor(p / 4)
						let m = p % 4

						if (n === '0') {
							zeroCount++
						} else {
							if (zeroCount > 0) {
								chineseStr += cnNums[0] // 添加零
							}
							zeroCount = 0 // 归零
							chineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m]
						}
						if (m === 0 && zeroCount < 4) {
							chineseStr += cnIntUnits[q]
						}
					}
					chineseStr += cnIntLast // 添加元
				}

				// 处理小数部分
				if (decimalNum) {
					const decLen = decimalNum.length > 4 ? 4 : decimalNum.length // 限制小数位数最多4位
					for (let i = 0; i < decLen; i++) {
						let n = decimalNum.charAt(i)
						if (n !== '0') {
							chineseStr += cnNums[Number(n)] + cnDecUnits[i]
						}
					}
				}

				// 处理没有小数部分的情况
				if (chineseStr === '') {
					chineseStr += cnNums[0] + cnIntLast + cnInteger
				} else if (decimalNum === undefined || decimalNum === '') {
					chineseStr += cnInteger // 添加整
				}

				return chineseStr
			},
			generatePaymentNoticeHtml(pageData) {
				// HTML 模板
				const template = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>缴款通知单</title>
  </head>
  <body style="font-family: 'SimSun' !important;">
    <img
      style="position: fixed;width:950px;height: 100%;z-index:-1;zoom:1"
      src="${config.imageUrl}/static/images/paymentNotice.jpg"
    />
	<div style="height:30px"></div>
    <main
      style=" width: 800px;
    height: auto;
    margin: auto;
    padding: 70px 60px;
    background-repeat: no-repeat;
    background-size: cover;"
    >
      <h1
        style="    text-align: center;
      font-size: 19px;
      font-weight: bold;
      color: #3d3d3d;
      margin-bottom: 40px;
	  font-family: SimSun;
	  "
      >
        农村产权交易服务平台缴款通知单
      </h1>
      <section>
        <h2
          style=" text-indent: 4em;     font-size: 15px;
        font-weight: 700;
        color: #3d3d3d;"
        >
          尊敬的${pageData.dealName}：
        </h2>
        <div
          style="  text-indent: 6em; /* 段落开头空两格 */
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          您好！为避免影响您参与交易，请您按以下要求及时缴纳款项。
        </div>
      </section>
      <section>
        <h2
          style=" text-indent: 4em;      font-size: 15px;
        font-weight: 700;
        color: #3d3d3d;"
        >
          缴款信息
        </h2>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          项目名称：${ pageData.proName
          }—标段${pageData.tendersCode}（${pageData.projectCode}）
        </div>
        <div
          style="  text-indent: 6em;
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          缴纳费种：${ pageData?.payFeeNameList?.join('、') }
        </div>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          缴纳金额：${pageData.dealMoney}元
		  &nbsp;&nbsp;
          ${this.convertCurrency(pageData.dealMoney)}
        </div>
        <div
          style="  text-indent: 6em;
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          缴费截止时间：${pageData.payEndTime || ''}
        </div>
      </section>
      <section>
        <h2
          style=" text-indent: 4em;      font-size: 15px;
        font-weight: 700;
        color: #3d3d3d;"
        >
          收款人信息
        </h2>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          收款户名：${pageData.virtualAccountName}
        </div>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          收款账号：${pageData.virtualAccount}
        </div>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          开户银行：${pageData.bankName}
        </div>
		<div style="
		text-indent: 6em; 
		font-weight: 400;
		font-size: 15px;
		color: #444444;
		line-height: 2.5;">
			开户网点：${pageData.openingBank||'--'}
		</div>
		<div style="   
		text-indent: 6em; 
		font-weight: 400;
		font-size: 15px;
		color: #444444;
		line-height: 2.5;">
			开户行号：${pageData.bankNo||'--'}
		</div>
      </section>
      <section>
        <h2
          style="text-indent: 4em;       font-size: 15px;
        font-weight: 700;
        color: #3d3d3d;"
        >
          特别提示：
        </h2>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          1、请按本通知单要求，一次性足额缴费。
        </div>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          2、为避免影响参与交易，请您至少在缴款截止时间前15分钟完成缴款。
        </div>
        <div
          style="  text-indent: 6em;
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          3、请您使用通过系统实名认证的银行卡进行缴款，否则无法缴款。
        </div>
        <div
          style="  text-indent: 6em; 
        font-weight: 400;
        font-size: 15px;
        color: #444444;
        line-height: 2.5;"
        >
          4、如有疑问，可与交易中心进行联系。
        </div>
      </section>
    </main>
  </body>
</html>

				`;

				return template;
			}
		}
	};
</script>

<style scoped lang="scss">
	view,
	div {
		word-break: break-all;
	}

	.FA4931 {
		color: #fa4931;
	}

	.wrap-box {
		padding: 30rpx 32rpx;

		.main-content {
			background-color: #fff;
			padding: 28rpx 20rpx;
			border-radius: 10rpx;

			.top {
				font-size: 28rpx;
				font-weight: 400;
				color: #666666;
				line-height: 42rpx;
				margin-bottom: 40rpx;
			}

			.bot {
				display: flex;
				margin-top: 10rpx;

				.key {
					width: 20%;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
				}

				.val {
					flex: 1;
					font-size: 28rpx;
					font-weight: 400;
					color: #fa4931;
					padding-left: 20rpx;
				}
			}
		}

		.tips-box {
			margin-top: 27rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #666666;
			line-height: 42rpx;
		}
	}

	.submit {
		width: 30%;
		height: 86rpx;
		background: #0098ff;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #ffffff;
		line-height: 86rpx;
	}

	.plain {
		background: transparent;
		border: 1rpx solid #0098ff;
		color: #0098ff;
	}

	.submit-box {
		height: 200rpx;
		background: #FFF;
		position: fixed;
		padding-top: 40rpx;
		bottom: 0rpx;
		margin: auto;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
	}
</style>