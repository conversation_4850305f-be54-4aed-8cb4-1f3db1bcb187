<template>
	<view class="termination-notice" :class="theme">
		<view class="banner">
			<u-swiper :height="'380rpx'" :list="pageData.bannerList" indicator indicatorMode="line" circular
				@change="swiperChange">
				<template #indicator>
					<view
						style="padding: 10rpx 20rpx;background-color: #000000;opacity: 0.8;border-radius: 4rpx;color: #fff;font-size: 32rpx;font-family: PingFang;font-weight: 400;color: #FFFFFF;font-weight: 100;">
						<text
							class="indicator-num__text">{{ pageData.currentNum + 1 }}/{{ pageData.bannerList.length }}</text>
					</view>
				</template>
			</u-swiper>
		</view>
		<view class="main-box">
			<view class="title">
				{{ pageData.infoData.proName ? pageData.infoData.proName:"--"}}
			</view>
			<view class="centent-box">
				<view class="centent-item">
					<view class="centent-item-name">
						项目编号
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.projectCode ? pageData.infoData.projectCode:"--" }}
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						项目位置
					</view>
					<view class="centent-item-main">
						<text>{{ pageData.infoData.address ? pageData.infoData.address:"--" }}</text>
						<view class="map" v-if="pageData.lat && pageData.lng"
							@click="toMap(pageData.lat, pageData.lng)">
							<img class="image" :src="config.imageUrl + '/static/images/vip/transactionAdvisory/map.png'"
								alt="" />
						</view>
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						交易品种
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.childTradeVarietyName ? pageData.infoData.childTradeVarietyName :"--" }}
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						交易方式
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.tradeType_dictText ? pageData.infoData.tradeType_dictText :"--" }}
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						交易面积
					</view>
					<view class="centent-item-main">
						<!-- 标段 -->
						<template v-if="pageData.infoData.noticeType == 2">
							<view v-if="pageData.infoData.tradeArea">
								{{ pageData.infoData.tradeArea ? pageData.infoData.tradeArea : "--" }}{{
								pageData.infoData.tradeAreaUnit_dictText }}
							</view>
							<view v-else>
								{{ parseFloat(pageData.infoData.tenderMatterArea) }}
								{{ pageData.infoData.tenderMatterAreaUnit }}
							</view>
						</template>
						<!-- 项目 -->
						<template v-else-if="pageData.infoData.noticeType == 1">
							<view v-if="pageData.infoData.matterVo && pageData.infoData.matterVo.matterArea">
								{{ parseFloat(pageData.infoData.matterVo.matterArea) }}
								{{ pageData.infoData.matterVo.matterAreaUnit }}
							</view>
							<view v-else>
								{{ pageData.infoData.tradeArea ? pageData.infoData.tradeArea : "--" }}{{
								pageData.infoData.tradeAreaUnit_dictText }}
							</view>
						</template>
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						流转期限
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.yearNum }}年{{ pageData.infoData.monthNum }}月
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						公告发布时间
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.publishTime ? pageData.infoData.publishTime:"--" }}
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						交易结果公示开始时间
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.resultAnnoStartTime ? pageData.infoData.resultAnnoStartTime : "--" }}
					</view>
				</view>

				<view class="centent-item">
					<view class="centent-item-name">
						交易结果公示结束时间
					</view>
					<view class="centent-item-main">
						{{ pageData.infoData.resultAnnoEndTime ? pageData.infoData.resultAnnoEndTime : "--" }}
					</view>
				</view>

				<view class="centent-item" v-if="pageData.infoData.matterVo?.tradeMatterVoList.length > 0">
					<view class="centent-item-name">
						交易标的物
					</view>
					<view class="centent-item-main" style="color: #0098FF;"
						@click="toSubjectMatterTable(pageData.infoData.matterVo)">
						查看
					</view>
				</view>
			</view>

			<!-- 			<ndb-collapse :title="'报名信息'" :collapseStyle="'margin-bottom: 32rpx;'">
				<view class="centent-box none-margin-padding">
					<template v-if="pageData.infoData.noticeSign">
						<view class="centent-item">
							<view class="centent-item-name">
								报名开始时间
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.signupBeginTime ? pageData.infoData.noticeSign.signupBeginTime :"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								报名结束时间
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.signupEndTime ? pageData.infoData.noticeSign.signupEndTime :"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								报名地点
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.signupAddress ? pageData.infoData.noticeSign.signupAddress:"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								报名缴款截至时间
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.signPayEndtime ? pageData.infoData.noticeSign.signPayEndtime:"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								交易时间
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.tradeTime ? pageData.infoData.noticeSign.tradeTime :"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								交易地点
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.tradeAddress ? pageData.infoData.noticeSign.tradeAddress :""}}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								联系人
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.contactPerson ? pageData.infoData.noticeSign.contactPerson:"--" }}
							</view>
						</view>

						<view class="centent-item">
							<view class="centent-item-name">
								联系电话
							</view>
							<view class="centent-item-main">
								{{ pageData.infoData.noticeSign.contactPhone ? pageData.infoData.noticeSign.contactPhone :"--" }}
							</view>
						</view>
					</template>
				</view>
			</ndb-collapse> -->

			<ndb-collapse :title="'标段信息' + (index + 1)" :collapseStyle="'margin-bottom: 32rpx;'"
				v-for="(item,index) in pageData.sectionList" :key="index">
				<view class="centent-box none-margin-padding" style="position: relative;">
					<view class="centent-item" v-for="(ite, ind) in item.templateParamVoList" :key="ind">
						<template v-if="ite.paramKey!=='serviceFeeRule'">
						<view class="centent-item-name">
							{{ ite.paramName }}
						</view>
						<view class="centent-item-main" v-if="ite.paramKey == 'yearNum'">
							{{ ite.defaultValue ? ite.defaultValue + '年' : ''
							}}{{ ite.optionSetValue ? ite.optionSetValue + '月' : '' }}
						</view>
						<view class="centent-item-main" v-else-if="ite.paramKey == 'rentIncRule'"
							style="display: flex;flex-direction: column;">
							<view v-for="(res1, i) in JSON.parse(ite.defaultValue)" :key="i">第{{ res1.year }}年递增上年金额的{{
								res1.values
								}}%</view>
						</view>
						<view class="centent-item-main"
							v-else-if="ite.paramKey == 'serviceFeeType'">
							<template v-if="item.serviceFeeType==='1'">不收取</template>
							<template v-if="item.serviceFeeType==='2'">按成交总额的比例收取（{{ item.serviceFeeLimit  }}）</template>
							<template v-if="item.serviceFeeType==='3'">{{ item.serviceFeeRule  }}元</template>
						</view>
						<view class="centent-item-main" style="display: flex;flex-direction: column;max-width: 70%;"
							v-else-if="ite.paramKey == 'serviceFeeRule' && ite.controlType == 16 && pageData.infoData.tradeType != 1">
							按成交价收取<text v-if="item.serviceFeeLimit">({{item.serviceFeeLimit}})</text>
						</view>
						<view class="centent-item-main" style="display: flex;flex-direction: column;max-width: 70%;"
							v-else-if="pageData.infoData.tradeType == 1 && ite.paramKey == 'serviceFeeRule' && ite.controlType == 16">
							按{{ ite.defaultValue }}年成交价收取<text v-if="item.serviceFeeLimit">({{item.serviceFeeLimit}})</text>
						</view>
						<view class="centent-item-main"
							v-else-if="ite.paramKey == 'serviceFeeRule' && ite.controlType == 17">
							服务费固定金额{{ ite.defaultValue ? ite.defaultValue + '元' : ite.defaultValue == 0 ? ite.defaultValue + '元' : ''
							}}
						</view>

						<view class="centent-item-main"
							v-else-if="ite.paramKey == 'perFeeRule' && ite.controlType == 17">
							履约金固定金额{{ ite.defaultValue }}元
						</view>

						<view class="centent-item-main"
							v-else-if="ite.paramKey == 'perFeeRule' && ite.controlType == 16 && pageData.infoData.tradeType != '1'">
							收取成交价的{{ ite.defaultValue }}%
						</view>
						<view class="centent-item-main"
							v-else-if="ite.paramKey == 'perFeeRule' && ite.controlType == 16 && pageData.infoData.tradeType == '1'">
							收取首年租金的{{ ite.defaultValue }}%
						</view>

						<view class="centent-item-main" v-else style="display: flex;flex-direction: column;max-width: 70%;" >
							{{ ite.defaultValue ? ite.defaultValue : "--" }}{{ ite.optionSetValue ? ite.optionSetValue : ""
							}}<text style="margin-left: 10px;"  v-if="ite.dictType=='DJDW'||ite.dictType=='money'">{{toChies(ite.defaultValue)}}</text>
							<text v-if="ite.defaultValue == '按比例收取'&&ite.paramKey=='serviceFeeType'&&item.serviceFeeLimit">({{item.serviceFeeLimit}})</text>
						</view>

					</template>
					</view>

					<template v-if="item.tradeFlag == '1'">
						<view class="centent-item" v-if="item.specialFlag==1">
							<view class="centent-item-name">
								成交人
							</view>
							<view class="centent-item-main">
								{{ item.traderPerson ? item.traderPerson :"--" }}
							</view>
						</view>

						<view class="centent-item" v-if="item.specialFlag==1">
							<view class="centent-item-name">
								成交总金额
							</view>
							<view class="centent-item-main">
								{{ item.tradeAmount ? item.tradeAmount + '元' :"--" }}
							</view>
						</view>

						<view class="centent-item" v-if="item.specialFlag==1">
							<view class="centent-item-name">
								溢价率(%)
							</view>
							<view class="centent-item-main">
								{{ item.overflowScale||item.overflowScale==0 ? item.overflowScale :"--" }}
								<!-- <text
									v-if="item.overflowScale">%</text> -->
							</view>
						</view>

						<view class="centent-item" v-if="item.specialFlag==1">
							<view class="centent-item-name">
								成交日期
							</view>
							<view class="centent-item-main">
								{{ item.tradeTime ? item.tradeTime :"--" }}
							</view>
						</view>
						<view class="centent-item" v-if="item.specialFlag==1">
							<view class="centent-item-name">
								成交单价
							</view>
							<view class="centent-item-main">
								{{ item.finalTradeUnitPrice ? item.finalTradeUnitPrice :"--" }}{{ item.floorPriceUnit_dictText}}
							</view>
						</view>
					</template>

					<view class="centent-item">
						<view class="centent-item-name">
							成交结果
						</view>
						<view class="centent-item-main">
							{{ item.tradeFlag == '1' ? '成功' : item.tradeFlag == '2' ? "失败":"--"}}
						</view>
					</view>

					<view class="centent-item" v-if="item.matterVo?.tradeMatterVoList.length > 0">
						<view class="centent-item-name">
							交易标的物
						</view>
						<view class="centent-item-main" style="color: #0098FF;"
							@click="toSubjectMatterTable(item.matterVo)">
							查看
						</view>
					</view>
					<view class="centent-item" v-if="item.accountRules">
						<view class="centent-item-name" style="min-width: 180rpx;">
							交易资金结转账户
						</view>
						<view class="centent-item-main"
							style="display: flex;flex-direction: column;align-items: flex-start;">
							<rich-text :nodes="item.accountRules" class="ql-editor" />
						</view>
					</view>

					<view class="position-cj">
						<img v-if="item.tradeFlag == '1'"
							:src="config.imageUrl + '/static/images/vip/transactionAdvisory/item2.png'" alt="" />
						<img v-if="item.tradeFlag == '2'"
							:src="config.imageUrl + '/static/images/vip/transactionAdvisory/item3.png'" alt="" />
					</view>
				</view>
			</ndb-collapse>

			<template v-if="isIHG" v-for="(item, index) in pageData.otherList">
				<ndb-collapse :title="item.templateParamVoList.length > 1 ?
						item.moduleName + (ind + 1) : item.moduleName
					" :collapseStyle="'margin-bottom: 32rpx;'" v-for="(ite, ind) in item.templateParamVoList" :key="ind">
					<view class="centent-box none-margin-padding">
						<view class="centent-item" v-for="(i, j) in ite" :key="j">
							<view class="centent-item-name">
								{{ i.paramName }}
							</view>
							<view class="centent-item-main" v-if="i.paramKey == 'yearNum'">
								<text>
									{{ i.defaultValue ? i.defaultValue + '年' : '' }}{{ i.optionSetValue ? i.optionSetValue +
										'月' :'' }}
								</text>
							</view>
							<view v-else-if="i.paramName === '交易标的物'" class="centent-item-main"
								style="color: var(--light-text);" @click="toSubjectMatterTable(i.defaultValue)">
								查看
							</view>
							<view class="centent-item-main" v-else-if="i.paramKey === 'addressLocation'">
								<text>
									{{ i.defaultValue.address||'--' }}
								</text>
								<view class="map" v-if="i.defaultValue.lat && i.defaultValue.lng"
									@click="toMap(i.defaultValue.lat, i.defaultValue.lng)">
									<image class="image"
										:src="config.imageUrl + '/static/images/vip/transactionAdvisory/map.png'"
										alt="" />
								</view>
							</view>
							<view class="centent-item-main" v-else-if="i.controlType == 9">
								<text>
									{{ i.defaultValue ? i.defaultValue : '--' }}{{
										i.otheFieldKeyValue ? '(' + i.otheFieldKeyValue + ')' :'' }}
								</text>
							</view>
							<view class="centent-item-main"
								v-else-if="![11, 12, 13, 14, 15, 16, 17].includes(i.controlType)">
								<text>
									{{ i.defaultValue ? i.defaultValue : '--' }}
								</text>
							</view>
							<view class="centent-item-main" v-else-if="[11, 12, 13, 14].includes(i.controlType)">
								<text>
									{{ i.defaultValue ? i.defaultValue : '--' }}
									{{ i.controlType==12&&i.optionSetValue?'至':'' }}
									{{ i.optionSetValue ? i.optionSetValue : "" }}
								</text>
							</view>
							<view class="centent-item-main" v-else-if="[15, 16, 17].includes(i.controlType)">
								<text>
									{{
										i.controlType == 15
											? "服务费收取规则展示"
											: i.controlType == 16
												? "履约保证金收取规则"
												: i.controlType == 17
													? "租金递增"
													: ""
									}}
								</text>
							</view>
							<view class="centent-item-main" v-else>
								<text>{{ i.defaultValue }}</text><text style="margin-left: 10px;"   v-if="i.dictType=='DJDW'||i.dictType=='money'">{{toChies(i.defaultValue)}}</text>
							</view>
						</view>
					</view>
				</ndb-collapse>
			</template>

			<ndb-collapse v-if="isIHG" title="附件信息" :collapseStyle="'margin-bottom: 32rpx;'">
				<view class="centent-box none-margin-padding">
					<view class="centent-item" v-for="(item,index) in pageData.fjList" :key="index">
						<view class="centent-item-name">
							{{ item.fileName }}：
						</view>
						<view class="centent-item-mainte">
							<view class=""
								style="color: #0098FF;font-weight: 400;font-size: 28rpx;line-height: 1.5;text-align: left;"
								v-for="(ite,ind) in item.fileList" :key="ind" @click="viewFile(ite)">
								{{ ite.fileName  }}
							</view>
						</view>
					</view>
				</view>
			</ndb-collapse>
			
			<ndb-collapse title="变更记录" :collapseStyle="'margin-bottom: 32rpx;'" v-if="changeRecordData.show">
				<view class="centent-box none-margin-padding change-box">
					<view style="height: auto;
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;flex-direction: column;background:#f1f8fe;padding: 20rpx 20rpx;" v-for="(item,index) in changeRecordData.list" :key="index"
						>
						<view  @click="toChangeAnnouncementDetails(item)" class="item" style="display: flex;flex-direction: row;justify-content: space-between;width: 100%;">
							<view class="title-text">
														{{ item.proName }}
													</view>
													<image :src="
							  config.imageUrl +
							  '/static/images/changeAnnouncementDetails/arrow.png'
							" mode="" class="arror"></image>
						</view>
						
					  <view v-if="item.fileList" style="display: flex;flex-direction: column;justify-content: left;width: 100%;background-color: #fff;padding: 20rpx 20rpx;border-radius: 10rpx;">
						  <view class=""  v-for="(itemChild,index2) in item.fileList" :key="index2" style="border-bottom: 1rpx solid #f1fff1;">
						  	<text>{{itemChild.fileName}}</text>
							<view>
								<view  @click="viewFile2(itemChild2)"  v-for="(itemChild2,index3) in itemChild.fileList" :key="index3" style="display: flex;flex-direction: column;justify-content: left;width: 100%;">
									<text style="color: #48b4ff;font-weight: 400;font-size: 28rpx;line-height: 1.5;text-align: left;">{{itemChild2.fileName}}</text>
								</view>
							</view>
						  </view>
					  </view>
					</view>
				</view>
			</ndb-collapse>
			
			<!-- <ndb-collapse title="变更附件" :collapseStyle="'margin-bottom: 32rpx;'" v-if="changeRecordData.show">
				<view class="centent-box none-margin-padding change-box">
					<view class="item" v-for="(item,index) in changeRecordData.list" :key="index"
						@click="toChangeAnnouncementDetails(item)">
						<view class="title-text">
							{{ item.proName }}
						</view>
						<image :src="
					    config.imageUrl +
					    '/static/images/changeAnnouncementDetails/arrow.png'
					  " mode="" class="arror"></image>
					</view>
				</view>
			</ndb-collapse> -->
			
			<view class="view-box" style="display: flex;flex-direction: row;flex-wrap: wrap;padding: 10px 0px 30px 0px ;">
				<text v-for="(item,index) in buttonList" :key="index" style="
								margin-left:10px;color: #0098FF;font-size: 12px;margin-top: 5px;text-align: left;
								display: inline-block;
								width: 30%;white-space: nowrap;
				overflow: hidden; 
				text-overflow: ellipsis; position: relative;
								" @click="tipClick(item.name,item.text,item.id)">{{item.name}}
								 <text v-if="index!=buttonList.length-1&&index!=2&&index!=5" style="display: inline-block;width: 1px;height: 10px;border-right: 1px solid #e1e1e1;position: absolute;top: 4px;right: 5px;"></text>
								 </text>
			</view>
		</view>
		<!-- 问题咨询 -->
		<view class="question-btn" @click="toConsultFn" v-if="isIHG">
			问题咨询
		</view>
	</view>
</template>

<script setup>
	import ndbCollapse from "@/components/ndb-collapse/ndb-collapse.vue";
	import defaultPdfImg from "@/static/uview/common/defaultPdf.png";
	import {
		onLoad
	} from "@dcloudio/uni-app";
	import {
		getCurrentInstance,
		reactive,
		ref
	} from 'vue';

	const {
		http
	} = uni.$u;

	import config from "@/common/config";

	let isIHG = ref(true)

	// #ifdef H5
	isIHG.value = !ipConfig2?.ihg?.showIndex
	console.log(isIHG.value, 'isIHG.value')
	// #endif
const changeRecordData = reactive({
		list: [],
		list2: [],
		show2: false,
		show: false
	})
	const changeRecordParam = reactive({
		projectId: "",
		tenderId: "",
		type: '2'
	})
	let pageData = reactive({
		currentNum: 0,
		bannerList: [],
		infoData: {

		},
		sectionList: [], // 标段
		otherList: [], // 其他数据项
		projectId: "",
		tenderId: "",
		lat: "",
		lng: "",
		fjList: [],
	})
	const theme = config.theme;
	// 初始化皮肤
	function initTheme() {
		// var storageTheme = uni.getStorageSync('theme');
		// if (!storageTheme) {
		// 	uni.setStorageSync('theme', "cq-mini-theme-blue")
		// }
		// theme.value = uni.getStorageSync('theme');
	}
	// 换肤
	function changeTheme() {
		// var storageTheme = uni.getStorageSync('theme');
		// if (storageTheme === "cq-mini-theme-blue") {
		// 	uni.setStorageSync('theme', "cq-mini-theme-orange")
		// 	config.imageUrl = "http://*************:9754/whcq-images";
		// }

		// if (storageTheme === "cq-mini-theme-orange") {
		// 	uni.setStorageSync('theme', "cq-mini-theme-blue")
		// 	config.imageUrl = "http://*************:9754/cq-images";
		// }

		// theme.value = uni.getStorageSync('theme');
	}
	function tipClick(text,name,id){
		uni.navigateTo({
			url: '/pages/userInformationView/tips?id='+id
		})
	}
	initTheme();
	let buttonList=ref([])
	onLoad((e) => {
		console.log(e);
		pageData.projectId = e.projectId;
		pageData.tenderId = e.tenderId;
		
		changeRecordParam.projectId = e.projectId;
		changeRecordParam.tenderId = e.tenderId;
		changeRecordParam.type = e.type;

		getData();
		getType();
		getChangeRecordData();
		getChangeRecordData2();
		http.get(
			config.baseUrl + `/appSupply/getBottomButtonConfig`, {
				method: 'GET'
			}
		).then(res => {
			if (res.data.code === 200) {
				buttonList.value=res.data.data;
			}
		})
		
		
	})
	//获取变更记录
const getChangeRecordData = () => {
		const data = {
			projectId: pageData.projectId,
			tenderId: pageData.tenderId,

		}

		// if (changeRecordParam.type !== '1') {
		// 	data.tenderId = changeRecordParam.tenderId;
		// }
		http.post(config.baseUrl + "/annoQuery/getRelevantResultChangeAnno", data, {
			method: "POST"
		}).then(r => {
			if (r.data.code !== 200) {
				uni.showToast({
					title: r.data.msg,
					icon: "none",
					duration: 3000,
				});
				return;
			}

			changeRecordData.list = r.data.data || [];

			if (changeRecordData.list.length) {
				changeRecordData.show = true;
			} else {

				changeRecordData.show = false;
			}

		})
		
		// http.post(config.baseUrl + "/annoQuery/getFileResultChangeAnno", data, {
		// 	method: "POST"
		// }).then(r => {
		// 	if (r.data.code !== 200) {
		// 		uni.showToast({
		// 			title: r.data.msg,
		// 			icon: "none",
		// 			duration: 3000,
		// 		});
		// 		return;
		// 	}
		
		// 	changeRecordData.list2 = r.data.data || [];
		
		// 	if (changeRecordData.list2.length) {
		// 		changeRecordData.show2 = true;
		// 	} else {
		
		// 		changeRecordData.show = false;
		// 	}
		
		// })
	}
	
	const toChangeAnnouncementDetails = (option) => {
		uni.navigateTo({
			url: "/pages/userInformationView/changeAnnouncementDetails2?changeAnnoId=" + option.annoChangeId +
				"&projectId=" + option.proId + "&tenderId=" + option.tenderId,
		});
	}
	let proType = ref(""); // true标段 false项目
	// 获取 项目公告还是标段公告
	const getType = () => {
		http.post(
			config.baseUrl + `/memberApp/subscribe/getAnnouncementType`, {
				projectId: pageData.projectId
			}, {
				method: 'GET'
			}
		).then(res => {
			if (res.data.code === 200) {
				proType.value = res.data.data
			}
		})
	}
let moneyUpper = ref("");

// 大写数字过滤器
function toChies(amount) {
  // 汉字的数字
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  const cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  const cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  const cnDecUnits = ["角", "分", "厘", "毫"];
  // 整数金额时后面跟的字符
  const cnInteger = "整";
  // 整型完以后的单位
  const cnIntLast = "元";
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = "";
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === "") {
    return "";
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return "";
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf(".") === -1) {
    integerNum = amount;

    decimalNum = "";
  } else {
    parts = amount.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== "") {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === "") {
    chineseStr += cnInteger;
  }
  moneyUpper.value = chineseStr;
  return chineseStr;
}
	// 预览图片
	function viewFile(url) {
		if (url.fileSuffix == "pdf") {
			uni.navigateTo({
				url: '/pages/userInformationView/pdfView?url=' + url.fileUrl,
			});
		} else {
			uni.previewImage({
				current: url, //当前的图片路径
				urls: [ // 所有图片的URL列表，数组格式   预览图片路径
					url.fileUrl
				],
				success: function(res) {
					console.log(res)
				}
			})
		}
	}
// 预览图片
	function viewFile2(url) {
		if (url.fileSuffix == "pdf") {
			uni.navigateTo({
				url: '/pages/userInformationView/pdfView?url=' + encodeURIComponent(url.fileUrl)+'&all=true',
			});
		} else {
			uni.previewImage({
				current: url, //当前的图片路径
				urls: [ // 所有图片的URL列表，数组格式   预览图片路径
					url.fileUrl
				],
				success: function(res) {
					console.log(res)
				}
			})
		}
	}

	function swiperChange(e) {
		console.log(e);
		pageData.currentNum = e.current;
	}

	// 点击立即报名
	function toLJBM(projectId, tenderId) {
		uni.navigateTo({
			url: '/pages/userInformationView/signUpNow?projectId=' + projectId +
				"&tenderId=" + tenderId
		});
	}

	// 去地图页面
	function toMap(lat, lng) {
		uni.navigateTo({
			url: '/pages/userInformationView/projectLocation?lat=' + lat +
				"&lng=" + lng
		});
	}
	const {
		proxy
	} = getCurrentInstance();
	// 获取数据
	function getData() {
		// 获取附件
		// http.post(config.baseUrl + '/memberApp/file/getTheAttachmentTypeList', {
		// 	busId: pageData.projectId,
		// 	projectId: pageData.projectId,
		// 	flowBasecode: proxy.ipConfig2.areaType
		// }, {
		// 	method: 'POST'
		// }).then(r => {
		// 	console.log(r);
		// 	if (r.data.code === 200) {
		// 		pageData.fjList = r.data.data;
		// 	}
		// })

		http.post(config.baseUrl + '/vipProject/getProjectBaseInfo', {
			projectId: pageData.projectId,
			tenderId: pageData.tenderId,
			queryType: "DEAL"
		}, {
			method: 'get'
		}).then(r => {
			if (r.data.code === 200) {
				console.log(r.data.data, 'r.data.data');

				pageData.infoData = r.data.data;
				if (r.data.data.images) {
					pageData.bannerList = r.data.data.images.map(item => {
						if (item.fileSuffix === "pdf") {
							return defaultPdfImg;
						}
						return item.fileUrl;
					});
				}
				if (pageData.bannerList.length === 0) {
					let appInfo = uni.getStorageSync('appInfo')
					pageData.bannerList = [appInfo.defaultUrl]
				}
				console.log(JSON.parse(r.data.data.addressLocation));
				pageData.lat = JSON.parse(r.data.data.addressLocation).lat;
				pageData.lng = JSON.parse(r.data.data.addressLocation).lng;

				// 获取附件
				http.post(config.baseUrl + '/memberApp/file/getTheAttachmentTypeList', {
					busId: pageData.infoData.resultNoticeId,
					projectId: pageData.projectId,
					flowBasecode: "8"
				}, {
					method: 'POST'
				}).then(r => {
					console.log(r);
					if (r.data.code === 200) {
						pageData.fjList = r.data.data;
					}
				})
			} else {
				// uni.showToast({
				// 	title: r.data.msg,
				// 	icon: 'none',
				// 	duration: 3000
				// });
			}
		})

		// 标段的
		let userid = "";
		if (uni.getStorageSync("user").certNo) {
			userid = uni.getStorageSync("user").certNo
		}
		http.post(config.baseUrl + '/vipProject/getProTenders', {
			projectId: pageData.projectId,
			tenderId: pageData.tenderId,
			queryType: "DEAL",
			certNo: userid
		}, {
			method: 'get'
		}).then(r => {
			if (r.data.code === 200) {
				r.data.data.forEach((item,index)=>{
					item.templateParamVoList.forEach((item2,index2)=>{
						if((item2.paramKey=='serviceFeeRule'||item2.paramKey=='serviceFeeType')&&item.serviceFeeFlag==1){
							let obj={
								paramName:'服务费应缴金额(元)',
								defaultValue:item.serviceFee,
							}
							item.templateParamVoList.splice(index2+1, 0, obj);
						}
					})
				})
				console.log(r.data.data)
				pageData.sectionList = r.data.data;
			} else {
				// uni.showToast({
				// 	title: r.data.msg,
				// 	icon: 'none',
				// 	duration: 3000
				// });
			}
		})

		// 其他数据
		http.post(config.baseUrl + '/vipProject/getProjectViewV2', {
			projectId: pageData.projectId,
			tenderId: pageData.tenderId
		}, {
			method: 'get'
		}).then(r => {
			if (r.data.code === 200) {
				pageData.otherList = r.data.data;
				pageData.otherList.forEach((item, index) => {
					if (item.moduleName === "基本信息") {
						item.templateParamVoList[0].forEach((item2, index2) => {
							if (item.matterFlag === 1) {
								let obj = {};
								if (item2.paramKey === "groupCode" && item.moduleType === 1 && item
									.matterVo) {
									obj = {
										paramName: "交易标的物",
										defaultValue: item.matterVo,
									};
									pageData.otherList[index].templateParamVoList[0].splice(
										index2 + 1, 0, obj)
								}
							}
						})
					}
				})
			} else {
				// uni.showToast({
				// 	title: r.data.msg,
				// 	icon: 'none',
				// 	duration: 3000
				// });
			}
		})
	}
	// 问题咨询
	const toConsultFn = () => {
		http.post(
			config.baseUrl + `/memberApp/memberName/getNameStatus`, {}, {
				method: 'GET'
			}
		).then(res => {
			if (res.data.code === 200) {
				if (res.data.data.userType == 1 || res.data.data.userType == 2) {
					uni.setStorageSync('userType', res.data.data.userType)
					uni.$u.route({
						url: 'pages/problemView/problemConsultationView/index',
						params: {
							"isFeedBack": "",
							"projectCode": pageData.infoData.projectCode,
							"projectId": pageData.projectId,
							"projectName": pageData.infoData.proName,
							"proId": pageData.infoData.proId
						}
					})
				}
			}
			if (res.data.code === 401) {
				uni.showToast({
					title: '用户未登录',
					icon: 'none',
					duration: 1500
				});
				setTimeout(() => {
					// 去登录页
					// uni.navigateTo({
					// 	url: '/pages/user/indexView/compontents/login/LoginLandingPage'
					// });


					if (isIHG.value) {
						ihgAppJssdk.user.login()
					} else {
						uni.navigateTo({
							url: '/pages/user/indexView/compontents/login/login'
						});
					}
				}, 1200);
			}
		})
	}


	// 新增需求 点击跳转标的物详情展示
	function toSubjectMatterTable(e) {
		console.log(e);
		uni.setStorageSync("matter", JSON.stringify(e))
		// 去登录页
		uni.navigateTo({
			url: '/pages/userInformationView/subjectMatterTable'
		});
	}
</script>
<script>
	export default {
		options: {
			styleIsolation: "shared"
		},
	};
</script>
<style lang="scss" scoped>
	.termination-notice {
		width: 100vw;
		min-height: 100vh;

		:deep(.u-collapse-item__content__text) {
			padding-top: 0;
		}

		:deep(.u-icon__icon--info) {
			color: #333;
		}

		:deep(.u-swiper__indicator) {
			bottom: 30rpx;
			right: 32rpx;
		}

		.banner {
			width: 100%;
			height: 380rpx;
		}

		.position-cj {
			position: absolute;
			right: 36rpx;
			top: 50%;
			width: 405rpx;
			height: 393rpx;

			// transform: translateY(-50%);
			// background-image: url();
			// background-repeat: no-repeat;
			// background-size: 100% 100%;
			image {
				width: 100%;
				height: 100%;
			}
		}

		.ljbm-button {
			margin-top: 20rpx;
			width: 100%;
			height: 88rpx;
			background: #0098FF;
			border-radius: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36rpx;
			font-family: PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
		}

		.main-box {
			width: 100%;
			min-height: 500rpx;
			border-radius: 20rpx 20rpx 0rpx 0rpx;
			background: #F7F7F7;
			transform: translateY(-20rpx);
			padding: 28rpx 32rpx;
			padding-bottom: 100rpx;

			.title {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 600;
				color: #333333;
				text-align: left;
				padding: 0 30rpx;
				line-height: 1;
				margin-bottom: 28rpx;
			}

			.centent-box {
				width: 100%;
				padding: 15rpx 30rpx;
				border-radius: 10rpx;
				background: #FFFFFF;
				margin-bottom: 30rpx;

				.centent-item {
					display: flex;
					padding: 12rpx 0;
					position: relative;
					justify-content: space-between;

					.centent-item-name {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #999;
						// line-height: 50rpx;
						margin-right: 20rpx;
					}

					.centent-item-main {
						// flex: 1;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #333333;
						// line-height: 50rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						align-items: center;
						max-width: 50%;
						text-align: right;

						.map {
							margin-left: 10rpx;
							// position: absolute;
							top: 8rpx;
							right: 0;
							min-width: 24rpx;
							width: 24rpx;
							height: 24rpx;
							// background-image: url("@/static/images/vip/transactionAdvisory/map.png");
							background-size: 100% 100%;
							background-repeat: no-repeat;

							.image {
								display: block;
								width: 100%;
								height: 100%;
							}
						}
					}
				}
			}

			.none-margin-padding {
				margin: 0;
				padding: 0;
			}
		}
	}

	.question-btn {
		position: fixed;
		left: 32rpx;
		bottom: 32rpx;
		line-height: 88rpx;
		width: 686rpx;
		height: 88rpx;
		text-align: center;
		color: #fff;
		background: var(--light-text);
		border-radius: 10rpx 10rpx 10rpx 10rpx;
	}
	.change-box {
		.item {
			height: 42rpx;
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
	
			&:nth-last-of-type(1) {
				margin-bottom: 0;
			}
	
			.type {
				width: 126rpx;
				min-width: 126rpx;
				height: 42rpx;
				border-radius: 10rpx 10rpx 10rpx 10rpx;
				margin-right: 10rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 24rpx;
			}
	
			.change {
				background: linear-gradient(270deg, #FF8A00 2%, #FF8A00 99%);
			}
	
			.transaction {
				background: #11B521;
			}
	
			.title-text {
				flex: 1;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				line-height: 28rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
	
			.arror {
				min-width: 32rpx;
				width: 32rpx;
				height: 32rpx;
				margin-left: 7rpx;
			}
		}
	}
</style>